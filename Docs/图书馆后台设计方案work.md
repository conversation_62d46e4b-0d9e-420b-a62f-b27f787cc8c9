你需要全面阅读我的工程代码和memorybank

凡是功能开发要严格参照已有相关代码和开发模式，
后台管理controller提供服务代码要在 com/aaron/spring/controller  一个大功能一个控制器
后台管理model代码在 com/aaron/spring/model
移动端控制器代码在 com/aaron/spring/api/v4  一个大功能一个控制器
移动前端model代码要在  com/aaron/spring/api/v4/model
service实现代码要在 com/aaron/spring/service/impl
service接口代码要在 com/aaron/spring/service
现有数据库结构可以参考 memory-bank/podcastDDL.sql


图书馆功能方案设计与实施步骤：


后台总体逻辑：
原有ADMIN角色是超级管理员，可以管理图书馆信息、配置图书馆管理员LIB_ADMIN，以及操作LIB_ADMIN的所有功能
不需要组织对象，只需要具体的图书馆对象，直接这个图书馆就服务某个机构。
图书馆对象的Key就使用ID，主要会用于mcp虚拟用户生成。
LIB_ADMIN能够管理本图书馆的组织、用户、藏书、借阅记录等
图书馆客户线下付款和确认书单，LIB_ADMIN拿到书单批量导入，导入信息至少包含书名、编号、数量，有批量删除功能和单独删除功能
所以ADMIN应该有个书单导出功能，带好书名和编号，编号不能改动，这样可以用于倒回到系统的图书馆。
确保只有对应机构的 LIB_ADMIN 角色的用户（图书馆管理员）能访问和操作其机构的管理页面。
LIB_ADMIN可以下架或上架协议范围内导入的书籍。默认全部上架。
图书馆用户的加入机制：
图书馆管理员LIB_ADMIN邀请用户，如果用户还未注册普通书城帐号，则用户收到邀请邮件，点击邀请链接，注册账号，然后加入图书馆。
如果用户已经注册普通书城帐号，则直接加入图书馆，并发送通知邮件。
管理员需要对帐号设置备注信息。
需要有检测用户是否加入图书馆的接口功能。
所有书都会先加入普通书城领域，图书馆的书都是从普通书城中选取的。
考虑图书预约记录存储和展示。
数据库设计为将来的简单图书馆统计要做好准备。
图书馆借阅规则：
一个人最多可以借10本书，14天到期，如果没有被其他人借走，则可以通过预约的方式一直续借，否则到期自动归回。
图书馆管理员可以给每本书设置借阅天数，默认14天。
一本书到期后，按照预约先后，给第一个预约的人，他将收到一封系统邮件提醒。
一本书的在一个图书馆同时可借阅数量， 与合作初始导入书籍数量相关。
一个人在一个图书馆预约的数量与借阅额度共享，加在一起不能超过10本。
系统管理员可以设置LIB_ADMIN、图书馆的备注信息。
图书馆管理员LIB_ADMIN可以看到每本书目前由谁所借


图书管理员角色应该是在这个层次实现 ，不用在libuser表中实现：     public interface RoleDBValue{

图书馆logo不需要
所有表不用加外键
顺序优先开发移动接口

在预约记录中需要给客户端一个时间数据，反馈预约后的排队预估时间，规则为：客户预约了一本书后，系统查询所有此书的副本，如果都被借阅了，则预估时间为第一个借阅此书副本到期的时间。
借这本书的本人也是通过预约供来再次排队。


数据库设计
```sql
-- 删除表（如果存在）
DROP TABLE IF EXISTS lib_reservation;
DROP TABLE IF EXISTS lib_borrow;
DROP TABLE IF EXISTS lib_book;
DROP TABLE IF EXISTS lib_user;
DROP TABLE IF EXISTS lib_library;
-- 图书馆表
CREATE TABLE `lib_library` (
                               `library_id` bigint AUTO_INCREMENT primary key,
                               `name` varchar(255) default '' COMMENT '图书馆名称',
                               `description` longtext  NULL COMMENT '图书馆描述',
                               `key` varchar(50) default '' COMMENT '图书馆唯一标识，用于LCP账号前缀',
                               `status` tinyint default 1 COMMENT '状态：0-禁用，1-启用',
                               `remark` varchar(500)  NULL COMMENT '备注信息',
                               `is_deleted` tinyint default 0 COMMENT '是否已经删除',
                               `created_at`    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
) collate = utf8mb4_unicode_ci COMMENT='图书馆表';
ALTER TABLE `lib_library`
    ADD COLUMN `max_borrow_books` int  DEFAULT 7 COMMENT '本图书馆内，每人最大借阅数量',
    ADD COLUMN `default_loan_days` int   DEFAULT 14 COMMENT '本图书馆内，默认借阅天数(天)';

-- 图书馆用户表
CREATE TABLE `lib_user` (
                            `lib_user_id` bigint AUTO_INCREMENT primary key,
                            `user_id` bigint default 0 COMMENT '关联用户ID  oc_customer',
                            `library_id` bigint default 0 COMMENT '关联图书馆ID',
                            `status` tinyint default 1 COMMENT '状态：0-拒绝，1-已邀请待确认，2-已确认加入',
                            `remark` varchar(500)  NULL COMMENT '用户备注',
                            `is_deleted` tinyint default 0 COMMENT '是否已经删除',
                            `created_at`    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)collate = utf8mb4_unicode_ci COMMENT='图书馆用户表';

-- 图书馆藏书表
CREATE TABLE `lib_book` (
                            `lib_book_id` bigint AUTO_INCREMENT  primary key,
                            `library_id` bigint default 0 COMMENT '关联图书馆ID',
                            `book_id` bigint default 0 COMMENT '关联书籍ID enyanbook',
                            `total_copies` int default 0 COMMENT '总藏书量',
                            `available_copies` int default 0 COMMENT '可用藏书量',
                            `status` tinyint default 1 COMMENT '状态：0-下架，1-上架',
                            `is_deleted` tinyint default 0 COMMENT '是否已经删除',
                            `created_at`    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)collate = utf8mb4_unicode_ci COMMENT='图书馆藏书表';
ALTER TABLE `lib_book`
    ADD COLUMN `loan_days` int DEFAULT NULL COMMENT '可借阅天数(天)，为空时使用图书馆默认值';

-- 图书馆借阅记录表
CREATE TABLE `lib_borrow` (
                              `lib_borrow_id` bigint AUTO_INCREMENT primary key,
                              `user_id` bigint default 0 COMMENT '用户ID',
                              `library_id` bigint default 0 COMMENT '图书馆ID',
                              `book_id` bigint default 0 COMMENT '书籍ID',
                              `purchase_id` bigint default 0 COMMENT '关联的LCP purchase记录ID',
                              `status` tinyint default 0 COMMENT '状态：0-借阅中，1-已归还，2-已逾期',
                              `borrow_at` datetime default CURRENT_TIMESTAMP COMMENT '借阅时间',
                              `due_at` datetime default CURRENT_TIMESTAMP COMMENT '应归还时间',
                              `return_at` datetime DEFAULT NULL COMMENT '实际归还时间',
                              `extend_count` int default 0 COMMENT '续借次数',
                              `created_at`    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)collate = utf8mb4_unicode_ci COMMENT='图书馆借阅记录表';
ALTER TABLE `lib_borrow`
    ADD COLUMN `group_name` varchar(30) DEFAULT '' COMMENT '分组名称',
    ADD COLUMN `group_name_time` bigint DEFAULT 0 COMMENT '分组信息更新时间';

-- 图书馆预约记录表
CREATE TABLE `lib_reservation` (
                                   `lib_reservation_id` bigint AUTO_INCREMENT primary key,
                                   `user_id` bigint default 0 COMMENT '用户ID',
                                   `library_id` bigint default 0 COMMENT '图书馆ID',
                                   `book_id` bigint default 0 COMMENT '书籍ID',
                                   `status` tinyint default 0 COMMENT '状态：0-等待中，1-可借阅，2-已取消，3-已完成，4-已过期',
                                   `reservation_at` datetime default CURRENT_TIMESTAMP COMMENT '预约时间',
                                   `expire_at` datetime default CURRENT_TIMESTAMP COMMENT '过期时间',
                                   `notification_at` datetime default CURRENT_TIMESTAMP COMMENT '通知时间',
                                   `created_at`  datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间',
                                   KEY `idx_library_book` (`library_id`, `book_id`),
                                   KEY `idx_status_expire` (`status`, `expire_at`)
)collate = utf8mb4_unicode_ci COMMENT='图书馆预约记录表';


```



代码包结构设计
按照您的要求，代码包结构如下：

Controller层：
```java

创建移动端REST控制器 (Rest Controllers)：
包路径: com.aaron.spring.api.v4.controller
RestLibraryController.java:
GET /library/me: 获取当前用户加入的所有机构/图书馆列表 (返回 List<UserMembershipInfo>)。
GET /library/{libraryId}: 获取特定机构/图书馆的详细信息 (返回 OrganizationInfo)。
GET /library/{libraryId}/books: 获取机构/图书馆的书籍列表 (分页, 可搜索/筛选, 返回 Page<OrganizationBookSummary>)。
GET /library/{libraryId}/books/{bookId}: 获取机构/图书馆中特定书籍的详情 (返回 OrganizationBookDetail)。
POST /library/books/borrow: 用户借阅图书 (请求体 BorrowBookRequest, 返回 LoanInfo 或操作结果)。
POST /library/books/return: 用户归还图书 (请求体 ReturnBookRequest, 返回操作结果)。
POST /library/books/extend: 用户延长借阅 (请求体 ExtendLoanRequest, 返回更新后的 LoanInfo 或操作结果)。
POST /library/books/reserve: 用户预约图书 (请求体 ReserveBookRequest, 返回 ReservationInfo 或操作结果)。
DELETE /library/reservations/{reservationId}: 用户取消预约。
GET /library/me/loans: 获取当前用户的所有（或指定状态的）借阅记录 (返回 List<LoanInfo>)。
GET /library/me/reservations: 获取当前用户的所有（或指定状态的）预约记录 (返回 List<ReservationInfo>)。
GET /library/{libraryId}/join-status: 查询用户是否加入了某个图书馆，以及加入状态 (返回 UserMembershipInfo 或简版状态)。
API安全配置 (Spring Security)：
所有API接口需要用户认证。
部分操作需要校验用户是否为该机构成员 (如借书)。




```



LCP集成方案：
借阅时在purchase表中创建类型为"LIBRARY_LOAN"的记录
生成虚拟用户ID：[library_key]_[user_email]
继续使用现有的LCP生成许可证流程
purchase类型也是LOAN


移动端API接口设计
1. 图书馆相关接口
   1.1 获取用户加入的图书馆列表
   CopyInsert
   GET /api/v4/library
   Response:
   {
   "code": 200,
   "message": "success",
   "data": [
   {
   "id": 1,
   "name": "T学院图书馆",
   "description": "T学院专用图书馆",
   "key": "tti_library",
   "status": 1,
   "memberStatus": 2, // 1-已邀请待确认，2-已确认加入
   "joinTime": "2025-06-01T10:00:00",
   "remark": "管理员备注"
   }
   ]
   }
   1.2 获取图书馆详情
   CopyInsert
   GET /api/v4/library/{libraryId}
   Response:
   {
   "code": 200,
   "message": "success",
   "data": {
   "id": 1,
   "name": "T学院图书馆",
   "description": "T学院专用图书馆",
   "key": "tti_library",
   "status": 1,
   "memberCount": 100,
   "bookCount": 500,
   "joinTime": "2025-06-01T10:00:00"
   }
   }
2. 图书相关接口
   2.1 获取图书馆图书列表
   CopyInsert
   GET /api/v4/library/{libraryId}/books
   Parameters:
- status: 0-全部，1-可借阅，2-已借阅
- keyword: 搜索关键词
- page: 页码
- size: 每页数量
  Response:
  {
  "code": 200,
  "message": "success",
  "data": {
  "total": 100,
  "list": [
  {
  "id": 1,
  "bookId": 1001,
  "title": "圣经",
  "author": "多人",
  "coverUrl": "https://example.com/cover.jpg",
  "totalCopies": 10,
  "availableCopies": 5,
  "status": 1,
  "borrowStatus": 0 // 0-未借阅，1-已借阅，2-已预约
  }
  ]
  }
  }
  2.2 获取图书详情
  CopyInsert
  GET /api/v4/library/{libraryId}/books/{bookId}
  Response:
  {
  "code": 200,
  "message": "success",
  "data": {
  "id": 1,
  "bookId": 1001,
  "title": "圣经",
  "author": "多人",
  "publisher": "中国基督教两会",
  "isbn": "9787505403165",
  "coverUrl": "https://example.com/cover.jpg",
  "description": "《圣经》是亚伯拉罕诸教(包括基督新教、天主教、东正教、犹太教等各宗教)的宗教经典...",
  "totalCopies": 10,
  "availableCopies": 5,
  "status": 1,
  "myBorrow": {
  "id": 1,
  "borrowTime": "2025-06-01T10:00:00",
  "dueTime": "2025-07-01T10:00:00",
  "returnTime": null,
  "status": 0,
  "canExtend": true,
  "extendCount": 0
  },
  "myReservation": null
  }
  }
3. 借阅相关接口
   3.1 借阅图书
   CopyInsert
   POST /api/v4/library/books/borrow
   Request:
   {
   "libraryId": 1,
   "bookId": 1001
   }
   Response:
   {
   "code": 200,
   "message": "借阅成功",
   "data": {
   "id": 1,
   "bookId": 1001,
   "title": "圣经",
   "borrowTime": "2025-06-09T20:00:00",
   "dueTime": "2025-07-09T20:00:00",
   "canExtend": true,
   "extendCount": 0
   }
   }
   3.2 归还图书
   CopyInsert
   POST /api/v4/library/books/return
   Request:
   {
   "borrowId": 1
   }
   Response:
   {
   "code": 200,
   "message": "归还成功",
   "data": {
   "id": 1,
   "returnTime": "2025-06-20T10:00:00"
   }
   }
   3.3 续借图书
   CopyInsert
   POST /api/v4/library/books/extend
   Request:
   {
   "borrowId": 1
   }
   Response:
   {
   "code": 200,
   "message": "续借成功",
   "data": {
   "id": 1,
   "dueTime": "2025-08-09T20:00:00",
   "extendCount": 1
   }
   }
   3.4 获取我的借阅记录
   CopyInsert
   GET /api/v4/library/my/borrows
   Parameters:
- status: 0-全部，1-借阅中，2-已归还，3-已逾期
- page: 页码
- size: 每页数量
  Response:
  {
  "code": 200,
  "message": "success",
  "data": {
  "total": 10,
  "list": [
  {
  "id": 1,
  "bookId": 1001,
  "title": "圣经",
  "coverUrl": "https://example.com/cover.jpg",
  "borrowTime": "2025-06-01T10:00:00",
  "dueTime": "2025-07-01T10:00:00",
  "returnTime": null,
  "status": 0,
  "canExtend": true,
  "extendCount": 0
  }
  ]
  }
  }
4. 预约相关接口
   4.1 预约图书
   CopyInsert
   POST /api/v4/library/books/reserve
   Request:
   {
   "libraryId": 1,
   "bookId": 1001
   }
   Response:
   {
   "code": 200,
   "message": "预约成功",
   "data": {
   "id": 1,
   "bookId": 1001,
   "title": "圣经",
   "reservationTime": "2025-06-09T20:00:00",
   "expireTime": "2025-06-16T20:00:00",
   "status": 0
   }
   }
   4.2 取消预约
   CopyInsert
   DELETE /api/v4/library/reservations/{reservationId}
   Response:
   {
   "code": 200,
   "message": "取消成功",
   "data": null
   }
   4.3 获取我的预约记录
   CopyInsert
   GET /api/v4/library/my/reservations
   Parameters:
- status: 0-全部，1-等待中，2-可借阅，3-已完成，4-已取消，5-已过期
- page: 页码
- size: 每页数量
  Response:
  {
  "code": 200,
  "message": "success",
  "data": {
  "total": 5,
  "list": [
  {
  "id": 1,
  "bookId": 1001,
  "title": "圣经",
  "coverUrl": "https://example.com/cover.jpg",
  "reservationTime": "2025-06-01T10:00:00",
  "expireTime": "2025-06-08T10:00:00",
  "status": 0,
  "canBorrow": false
  }
  ]
  }
  }
- 
- 





---
1. 后台管理Controller清单（以下所有图书管理的控制器应该合并为一个）
   1.1 LibraryAdminController - 图书馆管理
   java
   CopyInsert
   @Controller
   @RequestMapping("/admin/library")
   public class LibraryAdminController {

   // 图书馆列表
   @GetMapping("")
   public String listLibraries(Model model,
   @RequestParam(defaultValue = "1") int page,
   @RequestParam(defaultValue = "10") int size,
   @RequestParam(required = false) String keyword) {
   // 实现代码
   }

   // 新增图书馆页面
   @GetMapping("/add")
   public String addLibraryPage(Model model) {
   // 实现代码
   }

   // 保存图书馆
   @PostMapping("/save")
   public String saveLibrary(@Valid Library library, BindingResult result) {
   // 实现代码
   }

   // 编辑图书馆页面
   @GetMapping("/edit/{id}")
   public String editLibraryPage(@PathVariable Long id, Model model) {
   // 实现代码
   }

   // 更新图书馆
   @PostMapping("/update")
   public String updateLibrary(@Valid Library library, BindingResult result) {
   // 实现代码
   }

   // 删除图书馆
   @PostMapping("/delete/{id}")
   @ResponseBody
   public Result deleteLibrary(@PathVariable Long id) {
   // 实现代码
   }

   // 更新图书馆状态
   @PostMapping("/updateStatus")
   @ResponseBody
   public Result updateStatus(@RequestParam Long id, @RequestParam Integer status) {
   // 实现代码
   }
   }
   1.2 LibraryBookAdminController - 图书馆藏书管理
   java
   CopyInsert
   @Controller
   @RequestMapping("/admin/library/book")
   public class LibraryBookAdminController {

   // 图书馆藏书列表
   @GetMapping("")
   public String listBooks(@RequestParam Long libraryId,
   @RequestParam(defaultValue = "1") int page,
   @RequestParam(defaultValue = "10") int size,
   @RequestParam(required = false) String keyword,
   Model model) {
   // 实现代码
   }

   // 添加图书页面
   @GetMapping("/add")
   public String addBookPage(@RequestParam Long libraryId, Model model) {
   // 实现代码
   }

   // 保存图书
   @PostMapping("/save")
   public String saveBook(@Valid LibraryBook book, BindingResult result) {
   // 实现代码
   }

   // 批量导入图书页面
   @GetMapping("/import")
   public String importBookPage(@RequestParam Long libraryId, Model model) {
   // 实现代码
   }

   // 执行批量导入
   @PostMapping("/doImport")
   @ResponseBody
   public Result doImport(@RequestParam Long libraryId,
   @RequestParam("file") MultipartFile file) {
   // 实现代码
   }

   // 更新图书状态
   @PostMapping("/updateStatus")
   @ResponseBody
   public Result updateBookStatus(@RequestParam Long id,
   @RequestParam Integer status) {
   // 实现代码
   }

   // 调整库存
   @PostMapping("/adjustStock")
   @ResponseBody
   public Result adjustStock(@RequestParam Long id,
   @RequestParam Integer quantity) {
   // 实现代码
   }
   }
   1.3 LibraryUserAdminController - 图书馆用户管理
   java
   CopyInsert
   @Controller
   @RequestMapping("/admin/library/user")
   public class LibraryUserAdminController {

   // 用户列表
   @GetMapping("")
   public String listUsers(@RequestParam Long libraryId,
   @RequestParam(defaultValue = "1") int page,
   @RequestParam(defaultValue = "10") int size,
   @RequestParam(required = false) String keyword,
   Model model) {
   // 实现代码
   }

   // 添加用户页面
   @GetMapping("/add")
   public String addUserPage(@RequestParam Long libraryId, Model model) {
   // 实现代码
   }

   // 批量导入用户页面
   @GetMapping("/import")
   public String importUserPage(@RequestParam Long libraryId, Model model) {
   // 实现代码
   }

   // 执行批量导入
   @PostMapping("/doImport")
   @ResponseBody
   public Result doImport(@RequestParam Long libraryId,
   @RequestParam("file") MultipartFile file) {
   // 实现代码
   }

   // 发送邀请邮件
   @PostMapping("/sendInvitation")
   @ResponseBody
   public Result sendInvitation(@RequestParam Long libraryId,
   @RequestParam String email) {
   // 实现代码
   }

   // 批量发送邀请
   @PostMapping("/batchSendInvitation")
   @ResponseBody
   public Result batchSendInvitation(@RequestParam Long libraryId,
   @RequestParam List<String> emails) {
   // 实现代码
   }

   // 移除用户
   @PostMapping("/remove")
   @ResponseBody
   public Result removeUser(@RequestParam Long libraryId,
   @RequestParam Long userId) {
   // 实现代码
   }
   }
   1.4 LibraryBorrowAdminController - 借阅记录管理
   java
   CopyInsert
   @Controller
   @RequestMapping("/admin/library/borrow")
   public class LibraryBorrowAdminController {

   // 借阅记录列表
   @GetMapping("")
   public String listBorrows(@RequestParam Long libraryId,
   @RequestParam(defaultValue = "1") int page,
   @RequestParam(defaultValue = "10") int size,
   @RequestParam(required = false) Integer status,
   @RequestParam(required = false) String keyword,
   Model model) {
   // 实现代码
   }

   // 手动借出
   @PostMapping("/checkout")
   @ResponseBody
   public Result checkoutBook(@RequestParam Long libraryId,
   @RequestParam Long bookId,
   @RequestParam Long userId) {
   // 实现代码
   }

   // 手动归还
   @PostMapping("/return")
   @ResponseBody
   public Result returnBook(@RequestParam Long borrowId) {
   // 实现代码
   }

   // 续借
   @PostMapping("/renew")
   @ResponseBody
   public Result renewBook(@RequestParam Long borrowId) {
   // 实现代码
   }

   // 导出借阅记录
   @GetMapping("/export")
   public void exportBorrows(@RequestParam Long libraryId,
   @RequestParam(required = false) Integer status,
   @RequestParam(required = false) String startDate,
   @RequestParam(required = false) String endDate,
   HttpServletResponse response) {
   // 实现代码
   }
   }
   1.5 LibraryReservationAdminController - 预约记录管理
   java
   CopyInsert
   @Controller
   @RequestMapping("/admin/library/reservation")
   public class LibraryReservationAdminController {

   // 预约记录列表
   @GetMapping("")
   public String listReservations(@RequestParam Long libraryId,
   @RequestParam(defaultValue = "1") int page,
   @RequestParam(defaultValue = "10") int size,
   @RequestParam(required = false) Integer status,
   Model model) {
   // 实现代码
   }

   // 取消预约
   @PostMapping("/cancel")
   @ResponseBody
   public Result cancelReservation(@RequestParam Long reservationId) {
   // 实现代码
   }
   }
2. Service层接口清单
   2.1 LibraryService 接口
   java
   CopyInsert
   public interface LibraryService {
   // 分页查询图书馆列表
   PageInfo<Library> listLibraries(int page, int size, String keyword);

   // 根据ID获取图书馆
   Library getLibraryById(Long id);

   // 创建图书馆
   void createLibrary(Library library);

   // 更新图书馆
   void updateLibrary(Library library);

   // 删除图书馆
   void deleteLibrary(Long id);

   // 更新图书馆状态
   void updateLibraryStatus(Long id, Integer status);
   }
   2.2 LibraryBookService 接口
   java
   CopyInsert
   public interface LibraryBookService {
   // 分页查询图书馆藏书
   PageInfo<LibraryBook> listBooks(Long libraryId, int page, int size, String keyword);

   // 根据ID获取藏书
   LibraryBook getBookById(Long id);

   // 添加藏书
   void addBook(LibraryBook book);

   // 批量导入藏书
   void importBooks(Long libraryId, List<LibraryBook> books);

   // 更新藏书状态
   void updateBookStatus(Long id, Integer status);

   // 调整库存
   void adjustStock(Long id, Integer quantity);

   // 检查图书是否可借
   boolean isBookAvailable(Long bookId);
   }
   2.3 LibraryUserService 接口
   java
   CopyInsert
   public interface LibraryUserService {
   // 分页查询图书馆用户
   PageInfo<LibraryUser> listUsers(Long libraryId, int page, int size, String keyword);

   // 添加用户到图书馆
   void addUserToLibrary(LibraryUser libraryUser);

   // 批量导入用户
   void importUsers(Long libraryId, List<String> emails);

   // 发送邀请邮件
   void sendInvitation(Long libraryId, String email);

   // 批量发送邀请
   void batchSendInvitations(Long libraryId, List<String> emails);

   // 从图书馆移除用户
   void removeUserFromLibrary(Long libraryId, Long userId);

   // 确认加入图书馆
   void confirmJoin(Long libraryId, Long userId);
   }
   2.4 LibraryBorrowService 接口
   java
   CopyInsert
   public interface LibraryBorrowService {
   // 分页查询借阅记录
   PageInfo<LibraryBorrow> listBorrows(Long libraryId, int page, int size, Integer status, String keyword);

   // 借出图书
   LibraryBorrow checkoutBook(Long libraryId, Long bookId, Long userId);

   // 归还图书
   void returnBook(Long borrowId);

   // 续借图书
   LibraryBorrow renewBook(Long borrowId);

   // 获取用户借阅记录
   List<LibraryBorrow> getUserBorrows(Long userId, Integer status);

   // 检查用户是否可借阅
   boolean canUserBorrow(Long userId, Long libraryId);

   // 导出借阅记录
   void exportBorrows(Long libraryId, Integer status, String startDate, String endDate, HttpServletResponse response);
   }
   2.5 LibraryReservationService 接口
   java
   CopyInsert
   public interface LibraryReservationService {
   // 分页查询预约记录
   PageInfo<LibraryReservation> listReservations(Long libraryId, int page, int size, Integer status);

   // 创建预约
   LibraryReservation createReservation(Long libraryId, Long bookId, Long userId);

   // 取消预约
   void cancelReservation(Long reservationId);

   // 处理可借阅的预约
   void processAvailableReservations(Long bookId);

   // 获取图书的预约队列
   List<LibraryReservation> getBookReservationQueue(Long bookId);
   }
   2.6 LibraryNotificationService 接口
   java
   CopyInsert
   public interface LibraryNotificationService {
   // 发送邀请邮件
   void sendInvitationEmail(String toEmail, String libraryName, String confirmUrl);

   // 发送预约可借通知
   void sendReservationAvailableEmail(String toEmail, String libraryName, String bookTitle, String borrowUrl);

   // 发送借阅即将到期提醒
   void sendBorrowExpiringEmail(String toEmail, String libraryName, String bookTitle, String dueDate, String extendUrl);

   // 发送借阅逾期提醒
   void sendBorrowOverdueEmail(String toEmail, String libraryName, String bookTitle, String dueDate);

   // 发送预约即将过期提醒
   void sendReservationExpiringEmail(String toEmail, String libraryName, String bookTitle, String expireTime);
   }




  邮件通知设计
1. 通知类型
   邀请加入图书馆
   触发时机：管理员添加用户到图书馆
   收件人：被邀请用户
   内容：包含确认链接，点击后跳转到确认页面
   预约可借通知
   触发时机：预约的图书可借
   收件人：预约用户
   内容：通知用户预约的图书已可借阅，提供借阅链接
   借阅即将到期提醒
   触发时机：借阅到期前3天
   收件人：借阅用户
   内容：提醒用户借阅即将到期，提供续借链接
   借阅已逾期提醒
   触发时机：借阅逾期后每天
   收件人：借阅用户
   内容：提醒用户已逾期，请尽快归还
   预约即将过期提醒
   触发时机：预约即将过期前1天
   收件人：预约用户
   内容：提醒用户预约即将过期
2. 邮件模板
   邀请加入图书馆
   CopyInsert
   主题：您已被邀请加入{libraryName}图书馆

尊敬的{userName}：

您好！{adminName}邀请您加入{libraryName}图书馆。

请点击以下链接确认加入：
{confirmLink}

如果您不想加入，请忽略此邮件。

此致
{libraryName} 管理团队
预约可借通知
CopyInsert
主题：您预约的《{bookTitle}》可借阅了

尊敬的{userName}：

您好！您在{libraryName}图书馆预约的《{bookTitle}》现在可以借阅了。

请点击以下链接进行借阅：
{borrowLink}

此链接将在{expireTime}前有效，请及时处理。

此致
{libraryName} 管理团队
借阅即将到期提醒
CopyInsert
主题：您的借阅《{bookTitle}》即将到期

尊敬的{userName}：

您好！您在{libraryName}图书馆借阅的《{bookTitle}》将于{dueDate}到期。

请及时归还或点击以下链接续借：
{extendLink}

此致
{libraryName} 管理团队
实施步骤
第一阶段：移动端API开发（1周）
创建必要的DTO和VO类
实现图书馆相关接口
实现图书查询接口
实现借阅相关接口
实现预约相关接口
第二阶段：邮件通知服务（3天）
创建邮件模板
实现邮件发送服务
实现邮件队列处理
集成到业务逻辑中
第三阶段：后台管理功能（1周）
图书馆管理
用户管理
图书管理
借阅记录管理
预约记录管理
第四阶段：测试与优化（3天）
单元测试
集成测试
性能测试
安全测试
第五阶段：部署上线（1天）
数据库部署
后端服务部署
前端页面部署
监控告警配置
关键问题与注意事项
并发控制：
借书和预约操作需要加锁，避免超借
使用数据库乐观锁或分布式锁
事务管理：
借阅、归还、续借等操作需要保证数据一致性
使用@Transactional注解管理事务
性能优化：
频繁查询的接口添加缓存
分页查询优化
安全性：
接口权限控制
防止SQL注入和XSS攻击
敏感数据脱敏
可扩展性：
预留扩展字段
设计可配置的借阅规则












---
图书馆邮件系统设计
图书馆邮件机制实施方案
1. 重用现有代码
   enyan-web/src/main/java/com/aaron/spring/service/LibraryEmailService.java
   file_copy Copy
   package com.aaron.spring.service;

import com.aaron.spring.model.LibraryMember;
import com.aaron.spring.model.LibraryBorrow;
import com.aaron.spring.model.LibraryReservation;

/**
* @Author: Aaron Hao
* @Description: 图书馆邮件服务接口
* @Date: Created in 2023/07/01
* @Modified By:
  */
  public interface LibraryEmailService {

  /**
    * 发送图书馆邀请邮件
    * @param libraryId 图书馆ID
    * @param email 用户邮箱
    * @param libraryName 图书馆名称
    * @param confirmUrl 确认链接
      */
      void sendInvitationEmail(Long libraryId, String email, String libraryName, String confirmUrl);

  /**
    * 发送预约可借通知
    * @param reservation 预约信息
    * @param mailCode 邮件代码
      */
      void sendReservationEmail(LibraryReservation reservation, String mailCode);

  /**
    * 发送借阅相关通知
    * @param borrow 借阅信息
    * @param mailCode 邮件代码
      */
      void sendBorrowEmail(LibraryBorrow borrow, String mailCode);
      }
      enyan-web/src/main/java/com/aaron/spring/service/impl/LibraryEmailServiceImpl.java
      file_copy Copy
      package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.LibraryConstant;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.LibraryBorrow;
import com.aaron.spring.model.LibraryReservation;
import com.aaron.spring.service.EmailService;
import com.aaron.spring.service.LibraryEmailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
* @Author: Aaron Hao
* @Description: 图书馆邮件服务实现
* @Date: Created in 2023/07/01
* @Modified By:
  */
  @Slf4j
  @Service
  public class LibraryEmailServiceImpl implements LibraryEmailService {

  @Resource
  protected MessageSource messageSource;

  @Resource
  protected EmailService emailService;

  @Override
  public void sendInvitationEmail(Long libraryId, String email, String libraryName, String confirmUrl) {
  if (StringUtils.isBlank(email)) {
  return;
  }

       log.debug("sendInvitationEmail email={}, libraryId={}", email, libraryId);
       
       Map<String, Object> model = new HashMap<>();
       Mail mail = new Mail();
       mail.setModel(model);
       mail.setTo(email);
       mail.setFrom(Constant.EMAIL_FROM);
       mail.setLocale(Locale.SIMPLIFIED_CHINESE); // 默认简体中文，可根据用户设置调整
       
       mail.setSubject("您已被邀请加入图书馆");
       mail.setFtl("email/mail_library_invitation");
       
       model.put("emailToName", email);
       model.put("libraryName", libraryName);
       model.put("confirmUrl", confirmUrl);
       model.put("expireTime", DateFormatUtils.format(DateUtils.addDays(new Date(), 7), InterfaceContant.DateFormatCustom.DATE));
       
       emailService.sendMessageUsingThymeleafTemplate(mail);
  }

  @Override
  public void sendReservationEmail(LibraryReservation reservation, String mailCode) {
  if (reservation == null || StringUtils.isBlank(mailCode) || StringUtils.isBlank(reservation.getUserEmail())) {
  return;
  }

       log.debug("sendReservationEmail mailCode={}, reservationId={}", mailCode, reservation.getLibraryReservationId());
       
       Map<String, Object> model = new HashMap<>();
       Mail mail = new Mail();
       mail.setModel(model);
       mail.setTo(reservation.getUserEmail());
       mail.setFrom(Constant.EMAIL_FROM);
       mail.setLocale(Locale.SIMPLIFIED_CHINESE); // 默认简体中文，可根据用户设置调整
       
       model.put("emailToName", reservation.getUserEmail());
       model.put("bookTitle", reservation.getBookTitle());
       model.put("libraryName", reservation.getLibraryName());
       
       switch (mailCode) {
           case LibraryConstant.MailCode.RESERVATION_AVAILABLE:
               mail.setSubject("您预约的图书可借阅了");
               mail.setFtl("email/mail_library_reservation_available");
               model.put("expireTime", DateFormatUtils.format(DateUtils.addDays(new Date(), 3), InterfaceContant.DateFormatCustom.DATE));
               break;
           case LibraryConstant.MailCode.RESERVATION_EXPIRING:
               mail.setSubject("您的图书预约即将过期");
               mail.setFtl("email/mail_library_reservation_expiring");
               model.put("expireTime", DateFormatUtils.format(reservation.getExpireTime(), InterfaceContant.DateFormatCustom.DATE));
               break;
           // 可添加更多预约相关邮件类型
       }
       
       emailService.sendMessageUsingThymeleafTemplate(mail);
  }

  @Override
  public void sendBorrowEmail(LibraryBorrow borrow, String mailCode) {
  if (borrow == null || StringUtils.isBlank(mailCode) || StringUtils.isBlank(borrow.getUserEmail())) {
  return;
  }

       log.debug("sendBorrowEmail mailCode={}, borrowId={}", mailCode, borrow.getLibraryBorrowId());
       
       Map<String, Object> model = new HashMap<>();
       Mail mail = new Mail();
       mail.setModel(model);
       mail.setTo(borrow.getUserEmail());
       mail.setFrom(Constant.EMAIL_FROM);
       mail.setLocale(Locale.SIMPLIFIED_CHINESE); // 默认简体中文，可根据用户设置调整
       
       model.put("emailToName", borrow.getUserEmail());
       model.put("bookTitle", borrow.getBookTitle());
       model.put("libraryName", borrow.getLibraryName());
       model.put("dueTime", DateFormatUtils.format(borrow.getDueTime(), InterfaceContant.DateFormatCustom.DATE));
       
       switch (mailCode) {
           case LibraryConstant.MailCode.BORROW_EXPIRING:
               mail.setSubject("您借阅的图书即将到期");
               mail.setFtl("email/mail_library_borrow_expiring");
               break;
           case LibraryConstant.MailCode.BORROW_OVERDUE:
               mail.setSubject("您借阅的图书已逾期");
               mail.setFtl("email/mail_library_borrow_overdue");
               break;
           case LibraryConstant.MailCode.BORROW_EXTENDED:
               mail.setSubject("您的图书借阅已续借");
               mail.setFtl("email/mail_library_borrow_extended");
               break;
           // 可添加更多借阅相关邮件类型
       }
       
       emailService.sendMessageUsingThymeleafTemplate(mail);
  }
  }
2. 定义图书馆邮件代码常量
   enyan-web/src/main/java/com/aaron/spring/common/LibraryConstant.java
   file_copy Copy
   package com.aaron.spring.common;

/**
* @Author: Aaron Hao
* @Description: 图书馆常量定义
* @Date: Created in 2023/07/01
* @Modified By:
  */
  public interface LibraryConstant {

  /**
    * 图书馆邮件代码
      */
      interface MailCode {
      /**
        * 邀请加入图书馆
          */
          String INVITATION = "LIB_001";

      /**
        * 预约可借通知
          */
          String RESERVATION_AVAILABLE = "LIB_101";

      /**
        * 预约即将过期
          */
          String RESERVATION_EXPIRING = "LIB_102";

      /**
        * 借阅即将到期
          */
          String BORROW_EXPIRING = "LIB_201";

      /**
        * 借阅已逾期
          */
          String BORROW_OVERDUE = "LIB_202";

      /**
        * 借阅已续借
          */
          String BORROW_EXTENDED = "LIB_203";
          }

  /**
    * 图书馆用户角色
      */
      interface UserRole {
      /**
        * 普通用户
          */
          int NORMAL = 1;

      /**
        * 图书馆管理员
          */
          int ADMIN = 2;
          }

  /**
    * 借阅状态
      */
      interface BorrowStatus {
      /**
        * 借阅中
          */
          int BORROWING = 1;

      /**
        * 已归还
          */
          int RETURNED = 2;

      /**
        * 已逾期
          */
          int OVERDUE = 3;
          }

  /**
    * 预约状态
      */
      interface ReservationStatus {
      /**
        * 等待中
          */
          int WAITING = 1;

      /**
        * 可借阅
          */
          int AVAILABLE = 2;

      /**
        * 已完成
          */
          int COMPLETED = 3;

      /**
        * 已取消
          */
          int CANCELED = 4;

      /**
        * 已过期
          */
          int EXPIRED = 5;
          }
          }
3. 创建邮件模板
   enyan-web/src/main/webapp/WEB-INF/templates/email/mail_library_invitation.html
   file_copy Copy
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>图书馆邀请</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .margin-bottom-1x {
            margin-bottom: 1rem;
        }
        .hr-light {
            border-top: 1px solid #eee;
        }
        .hr {
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
<!-- logo -->
<div class="margin-bottom-1x"><a href="https://e.bookapp.cc/"><img src="https://e.bookapp.cc/img/logo/logo.png" width="120" height="36"></a></div>
<!-- content -->
<div class="container">
    <!-- info -->
    <p th:text="#{mail.greetings(${emailToName})}"><EMAIL>，您好！</p>
    <p>您已被邀请加入<strong th:text="${libraryName}">XXX图书馆</strong>。</p>
    <p>请点击以下链接确认加入：</p>
    <p><a th:href="${confirmUrl}" th:text="${confirmUrl}">https://e.bookapp.cc/library/confirm?token=xxx</a></p>
    <p>此链接将在<strong th:text="${expireTime}">2023-07-08</strong>前有效，请及时处理。</p>
    <p>如果您不想加入，请忽略此邮件。</p>
</div>
<hr class="hr-light margin-bottom-1x">
<hr class="hr margin-bottom-1x">
<!-- Copyright-->
<p class="text-sm text-center">© 恩道电子书</p>
</body>
</html>
enyan-web/src/main/webapp/WEB-INF/templates/email/mail_library_borrow_expiring.html
file_copy Copy
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>借阅即将到期提醒</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .margin-bottom-1x {
            margin-bottom: 1rem;
        }
        .hr-light {
            border-top: 1px solid #eee;
        }
        .hr {
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
<!-- logo -->
<div class="margin-bottom-1x"><a href="https://e.bookapp.cc/"><img src="https://e.bookapp.cc/img/logo/logo.png" width="120" height="36"></a></div>
<!-- content -->
<div class="container">
    <!-- info -->
    <p th:text="#{mail.greetings(${emailToName})}"><EMAIL>，您好！</p>
    <p>您在<strong th:text="${libraryName}">XXX图书馆</strong>借阅的《<strong th:text="${bookTitle}">书名</strong>》将于<strong th:text="${dueTime}">2023-07-15</strong>到期。</p>
    <p>请及时归还或登录恩道电子书App进行续借。</p>
    <p>如有其他用户预约此书，到期后将无法续借。</p>
</div>
<hr class="hr-light margin-bottom-1x">
<hr class="hr margin-bottom-1x">
<!-- Copyright-->
<p class="text-sm text-center">© 恩道电子书</p>
</body>
</html>
4. 创建定时任务发送邮件
enyan-web/src/main/java/com/aaron/spring/task/LibraryEmailTask.java
file_copy Copy
package com.aaron.spring.task;

import com.aaron.spring.common.LibraryConstant;
import com.aaron.spring.model.LibraryBorrow;
import com.aaron.spring.model.LibraryReservation;
import com.aaron.spring.service.LibraryBorrowService;
import com.aaron.spring.service.LibraryEmailService;
import com.aaron.spring.service.LibraryReservationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* @Author: Aaron Hao
* @Description: 图书馆邮件定时任务
* @Date: Created in 2023/07/01
* @Modified By:
  */
  @Slf4j
  @Component
  public class LibraryEmailTask {

  @Resource
  private LibraryBorrowService libraryBorrowService;

  @Resource
  private LibraryReservationService libraryReservationService;

  @Resource
  private LibraryEmailService libraryEmailService;

  /**
    * 借阅即将到期提醒（到期前3天）
    * 每天凌晨1点执行
      */
      @Scheduled(cron = "0 0 1 * * ?")
      public void sendBorrowExpiringEmails() {
      log.debug("开始发送借阅即将到期提醒邮件");
      List<LibraryBorrow> borrows = libraryBorrowService.findBorrowsExpiringInDays(3);
      for (LibraryBorrow borrow : borrows) {
      libraryEmailService.sendBorrowEmail(borrow, LibraryConstant.MailCode.BORROW_EXPIRING);
      }
      log.debug("借阅即将到期提醒邮件发送完成，共{}封", borrows.size());
      }

  /**
    * 借阅已逾期提醒
    * 每天凌晨1点30分执行
      */
      @Scheduled(cron = "0 30 1 * * ?")
      public void sendBorrowOverdueEmails() {
      log.debug("开始发送借阅已逾期提醒邮件");
      List<LibraryBorrow> borrows = libraryBorrowService.findOverdueBorrows(1); // 逾期1天
      for (LibraryBorrow borrow : borrows) {
      libraryEmailService.sendBorrowEmail(borrow, LibraryConstant.MailCode.BORROW_OVERDUE);
      }
      log.debug("借阅已逾期提醒邮件发送完成，共{}封", borrows.size());
      }

  /**
    * 预约即将过期提醒（过期前1天）
    * 每天凌晨2点执行
      */
      @Scheduled(cron = "0 0 2 * * ?")
      public void sendReservationExpiringEmails() {
      log.debug("开始发送预约即将过期提醒邮件");
      List<LibraryReservation> reservations = libraryReservationService.findReservationsExpiringInDays(1);
      for (LibraryReservation reservation : reservations) {
      libraryEmailService.sendReservationEmail(reservation, LibraryConstant.MailCode.RESERVATION_EXPIRING);
      }
      log.debug("预约即将过期提醒邮件发送完成，共{}封", reservations.size());
      }
      }
5. 在业务逻辑中调用邮件服务
   enyan-web/src/main/java/com/aaron/spring/service/impl/LibraryUserServiceImpl.java
   file_copy Copy
   // 在用户邀请方法中
   @Override
   public void sendInvitation(Long libraryId, String email) {
   // 业务逻辑处理...

   // 生成确认链接
   String token = generateInvitationToken(libraryId, email);
   String confirmUrl = WebUtil.getBasePath() + "/api/v4/library/join/confirm?token=" + token;

   // 发送邀请邮件
   libraryEmailService.sendInvitationEmail(libraryId, email, library.getName(), confirmUrl);

   // 记录邀请信息



















---图书馆分组设计方案--- https://app.augmentcode.com/share/D-SVwAIScdM
目前的分组是依附在enyanbookbuy上的，但是图书馆没有这个对象，评估是否可以使用lib_borrow对象记录分组，如果可行，方案会是怎么样

Augment
使用lib_borrow对象记录图书馆分组的可行性评估
现有分组机制分析
目前系统中的分组功能依附于EnyanBookBuy对象：

enyan-web/src/main/java/com/aaron/spring/model/EnyanBookBuy.java
file_copy Copy
public class EnyanBookBuy extends BaseDTO{
// ...
private String groupName;
@JsonSerialize(using=Long2String.class)
@JsonDeserialize(using=String2Long.class)
private Long groupNameTime;
// ...
}
分组更新方法：

enyan-web/src/main/java/com/aaron/spring/service/impl/EnyanBookBuyServiceImpl.java
file_copy Copy
@Override
public int updateGroupNameByEmailAndBookId(EnyanBookBuy bookBuy) {
return enyanBookBuyCustomMapper.updateGroupNameByEmailAndBookId(bookBuy);
}

@Override
public int updateGroupNameByEmailAndBookIds(EnyanBookBuy bookBuy, List<Long> idList) {
Long[] ids = idList.stream().toArray(Long[]::new);
return enyanBookBuyCustomMapper.updateGroupNameByEmailAndBookIds(bookBuy, ids);
}
lib_borrow表结构分析
lib_borrow表的结构如下：

Docs/图书馆后台设计方案work.md
file_copy Copy
CREATE TABLE `lib_borrow` (
`lib_borrow_id` bigint AUTO_INCREMENT primary key,
`user_id` bigint default 0 COMMENT '用户ID',
`library_id` bigint default 0 COMMENT '图书馆ID',
`book_id` bigint default 0 COMMENT '书籍ID',
`purchase_id` bigint default 0 COMMENT '关联的LCP purchase记录ID',
`status` tinyint default 0 COMMENT '状态：0-借阅中，1-已归还，2-已逾期',
`borrow_at` datetime default CURRENT_TIMESTAMP COMMENT '借阅时间',
`due_at` datetime default CURRENT_TIMESTAMP COMMENT '应归还时间',
`return_at` datetime DEFAULT NULL COMMENT '实际归还时间',
`extend_count` int default 0 COMMENT '续借次数',
`created_at` datetime(6) default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
可行性评估
优势
数据关联已存在：lib_borrow已经关联了用户、图书馆和书籍
状态管理：已有状态字段可以标识借阅状态
时间记录：包含多个时间字段，便于管理分组时间线
劣势
缺少分组字段：当前表结构没有分组相关字段
功能混淆：借阅记录和分组是两个不同的业务概念
历史记录问题：归还后的记录如何保持分组关系
实施方案
如果决定使用lib_borrow记录分组，建议采取以下方案：

1. 表结构修改
   Docs/图书馆后台设计方案work.md
   file_copy Copy
   -- 修改lib_borrow表，添加分组相关字段
   ALTER TABLE `lib_borrow`
   ADD COLUMN `group_name` varchar(30) DEFAULT '' COMMENT '分组名称',
   ADD COLUMN `group_name_time` bigint DEFAULT 0 COMMENT '分组信息更新时间';
2. 服务层适配
   enyan-web/src/main/java/com/aaron/spring/service/LibBorrowService.java
   file_copy Copy
   package com.aaron.spring.service;

import com.aaron.spring.model.LibBorrow;
import java.util.List;

public interface LibBorrowService {
// 现有借阅相关方法...

    /**
     * 更新单本书籍的分组信息
     */
    int updateGroupNameByUserIdAndBookId(LibBorrow libBorrow);
    
    /**
     * 批量更新书籍的分组信息
     */
    int updateGroupNameByUserIdAndBookIds(LibBorrow libBorrow, List<Long> bookIds);
    
    /**
     * 获取用户在特定图书馆的所有分组
     */
    List<String> getUserGroupNames(Long userId, Long libraryId);
    
    /**
     * 获取用户在特定图书馆特定分组的所有书籍
     */
    List<LibBorrow> getBooksByUserIdAndGroupName(Long userId, Long libraryId, String groupName);
}
3. 实现类
   enyan-web/src/main/java/com/aaron/spring/service/impl/LibBorrowServiceImpl.java
   file_copy Copy
   package com.aaron.spring.service.impl;

import com.aaron.spring.mapper.LibBorrowMapper;
import com.aaron.spring.mapper.custom.LibBorrowCustomMapper;
import com.aaron.spring.model.LibBorrow;
import com.aaron.spring.service.LibBorrowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class LibBorrowServiceImpl implements LibBorrowService {

    @Autowired
    private LibBorrowMapper libBorrowMapper;
    
    @Autowired
    private LibBorrowCustomMapper libBorrowCustomMapper;
    
    // 实现借阅相关方法...
    
    @Override
    public int updateGroupNameByUserIdAndBookId(LibBorrow libBorrow) {
        return libBorrowCustomMapper.updateGroupNameByUserIdAndBookId(libBorrow);
    }
    
    @Override
    public int updateGroupNameByUserIdAndBookIds(LibBorrow libBorrow, List<Long> bookIds) {
        Long[] ids = bookIds.stream().toArray(Long[]::new);
        return libBorrowCustomMapper.updateGroupNameByUserIdAndBookIds(libBorrow, ids);
    }
    
    @Override
    public List<String> getUserGroupNames(Long userId, Long libraryId) {
        return libBorrowCustomMapper.getUserGroupNames(userId, libraryId);
    }
    
    @Override
    public List<LibBorrow> getBooksByUserIdAndGroupName(Long userId, Long libraryId, String groupName) {
        return libBorrowCustomMapper.getBooksByUserIdAndGroupName(userId, libraryId, groupName);
    }
}
4. 自定义Mapper
   enyan-web/src/main/java/com/aaron/spring/mapper/custom/LibBorrowCustomMapper.java
   file_copy Copy
   package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.LibBorrow;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.util.List;

public interface LibBorrowCustomMapper {

    @Update({"update lib_borrow set group_name=#{record.groupName}, group_name_time=#{record.groupNameTime} " +
             "where user_id=#{record.userId} and book_id=#{record.bookId} and library_id=#{record.libraryId}"})
    int updateGroupNameByUserIdAndBookId(@Param("record") LibBorrow record);
    
    @Update({"<script>update lib_borrow set group_name=#{record.groupName}, group_name_time=#{record.groupNameTime} " +
             "where user_id=#{record.userId} and library_id=#{record.libraryId} " +
             "and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
             "</script>"})
    int updateGroupNameByUserIdAndBookIds(@Param("record") LibBorrow record, @Param("ids") Long[] ids);
    
    @Select({"select distinct group_name from lib_borrow where user_id=#{userId} and library_id=#{libraryId} " +
             "and group_name != '' and status=0"})
    List<String> getUserGroupNames(@Param("userId") Long userId, @Param("libraryId") Long libraryId);
    
    @Select({"select * from lib_borrow where user_id=#{userId} and library_id=#{libraryId} " +
             "and group_name=#{groupName} and status=0"})
    List<LibBorrow> getBooksByUserIdAndGroupName(@Param("userId") Long userId, 
                                                @Param("libraryId") Long libraryId, 
                                                @Param("groupName") String groupName);
}
5. REST API 适配
   enyan-web/src/main/java/com/aaron/spring/api/v4/controller/RestLibraryController.java
   file_copy Copy
   package com.aaron.spring.api.v4.controller;

import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestLibraryBook;
import com.aaron.spring.model.LibBorrow;
import com.aaron.spring.service.LibBorrowService;
import com.aaron.util.ExecuteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/v4/library")
public class RestLibraryController extends RestBaseController {

    @Autowired
    private LibBorrowService libBorrowService;
    
    /**
     * 获取用户在图书馆的所有分组
     */
    @RequestMapping(value = "/groups", method = RequestMethod.POST)
    public ExecuteResult<List<String>> getLibraryGroups(@RequestBody RestLibraryBook request, 
                                                       HttpServletRequest httpRequest) {
        request.initHeaderValue(httpRequest);
        ExecuteResult<List<String>> result = new ExecuteResult<>();
        
        if (request.getLibraryId() == null || request.getUserId() == null) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        
        List<String> groups = libBorrowService.getUserGroupNames(request.getUserId(), request.getLibraryId());
        result.setResult(groups);
        
        return result;
    }
    
    /**
     * 更新图书馆书籍分组
     */
    @RequestMapping(value = "/updateGroup", method = RequestMethod.POST)
    public ExecuteResult<String> updateBookGroup(@RequestBody RestLibraryBook request, 
                                               HttpServletRequest httpRequest) {
        request.initHeaderValue(httpRequest);
        ExecuteResult<String> result = new ExecuteResult<>();
        
        if (request.getLibraryId() == null || request.getUserId() == null || 
            request.getBookIds() == null || request.getBookIds().isEmpty()) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        
        LibBorrow libBorrow = new LibBorrow();
        libBorrow.setUserId(request.getUserId());
        libBorrow.setLibraryId(request.getLibraryId());
        libBorrow.setGroupName(request.getGroupName() == null ? "" : request.getGroupName());
        libBorrow.setGroupNameTime(System.currentTimeMillis());
        
        int updated = libBorrowService.updateGroupNameByUserIdAndBookIds(libBorrow, request.getBookIds());
        result.setResult("更新成功，影响" + updated + "条记录");
        
        return result;
    }
}
方案评估
优点
简化架构：不需要创建新表，减少系统复杂度
功能整合：借阅和分组功能整合在一起，便于管理
数据一致性：确保只有借阅中的书籍才能被分组
缺点
概念混淆：借阅和分组是不同的业务概念，混合可能导致理解困难
功能限制：只有借阅中的书籍才能被分组，归还后分组信息可能丢失
扩展性问题：如果未来分组功能需要更复杂的属性，可能需要再次修改表结构
替代方案
如果上述缺点影响较大，可以考虑创建独立的分组表：

Docs/图书馆后台设计方案work.md
file_copy Copy
-- 图书馆用户分组表
CREATE TABLE `lib_user_group` (
`group_id` bigint AUTO_INCREMENT primary key,
`user_id` bigint default 0 COMMENT '用户ID',
`library_id` bigint default 0 COMMENT '图书馆ID',
`group_name` varchar(30) default '' COMMENT '分组名称',
`created_at` datetime(6) default '2020-01-01 00:00:00.000000' null comment '创建时间',
`updated_at` datetime(6) default '2020-01-01 00:00:00.000000' null comment '更新时间'
)collate = utf8mb4_unicode_ci COMMENT='图书馆用户分组表';

-- 图书馆书籍分组关系表
CREATE TABLE `lib_book_group_rel` (
`rel_id` bigint AUTO_INCREMENT primary key,
`group_id` bigint default 0 COMMENT '分组ID',
`book_id` bigint default 0 COMMENT '书籍ID',
`lib_borrow_id` bigint default 0 COMMENT '关联的借阅记录ID',
`created_at` datetime(6) default '2020-01-01 00:00:00.000000' null comment '创建时间'
)collate = utf8mb4_unicode_ci COMMENT='图书馆书籍分组关系表';
结论与建议
基于以上分析，我提出以下建议：

短期解决方案：如果图书馆功能需要快速上线，且分组功能相对简单，可以采用修改lib_borrow表的方案。
长期解决方案：如果预计分组功能会不断扩展，或者需要支持更复杂的分组逻辑（如嵌套分组、分组共享等），建议采用独立分组表的方案。
折中方案：先在lib_borrow表中添加分组字段，同时设计独立的分组表结构，但暂不实现。当业务需求扩展时，可以平滑迁移到独立分组表。
无论采用哪种方案，都应确保与现有的EnyanBookBuy分组逻辑保持一致的用户体验，让用户在不同场景下使用分组功能时感受一致。







---图书馆笔记解决方案---- https://app.augmentcode.com/share/vjFW1p_BwLU
请再全面分析，现有笔记功能，是否可以直接用在图书馆，要求是一本书的笔记跟随这个人就行。可能存在什么问题

Augment
图书馆笔记功能适配分析
现有笔记功能分析
根据代码库，系统中存在多种笔记功能：

1. 灵修笔记 (EnyanPlanNote)
   enyan-web/src/main/java/com/aaron/spring/model/EnyanPlanNote.java
   file_copy Copy
   public class EnyanPlanNote extends BaseDTO{
   private static final long serialVersionUID = -3107086237154459569L;
   @JsonSerialize(using=Long2String.class)
   @JsonDeserialize(using=String2Long.class)
   private Long planNoteId;

   private String userEmail;

   @JsonSerialize(using=Long2String.class)
   @JsonDeserialize(using=String2Long.class)
   private Long bookId;

   private Integer day;

   private String dayName;

   private String note;

   private Integer isDeleted;

   @JsonSerialize(using=Long2String.class)
   @JsonDeserialize(using=String2Long.class)
   private Long createTime;

   @JsonSerialize(using=Long2String.class)
   @JsonDeserialize(using=String2Long.class)
   private Long updateTime;
   }
2. 划线笔记 (EnyanUserHighlights)
   从数据库ER图中可以看到：

enyan_user_highlights {
varchar_255_ highlight_id PK
bigint user_id "读者ID, default 0"
varchar_96_ user_email "用户email, default ''"
bigint book_id "book ID, default 0"
varchar_250_ content "划线内容, default ''"
bigint create_time "创建时间, default 0"
varchar_100_ rangy "rangy, default ''"
varchar_2000_ note_for_highlight "备注, default ''"
varchar_100_ chapter_name "章节标题, default ''"
int page_chapter "当前章节, default 0"
int current_page "当前所在章节内的页面, default 0"
int total_page "本章节总页面数, default 0"
int page_process "当前进度*1000, default 0"
int is_deleted "是否已经删除, default 0"
bigint update_time "同步时间, default 0"
}
3. 书签功能 (EnyanUserBookmarks)
   enyan_user_bookmarks {
   varchar_100_ bookmark_id PK
   bigint user_id "读者ID, default 0"
   varchar_96_ user_email "用户email, default ''"
   bigint book_id "book ID, default 0"
   bigint create_time "创建时间, default 0"
   varchar_100_ chapter_name "章节标题, default ''"
   int page_chapter "当前章节, default 0"
   int current_page "当前所在章节内的页面, default 0"
   int total_page "本章节总页面数, default 0"
   int page_process "当前进度*1000, default 0"
   int is_deleted "是否已经删除, default 0"
   bigint update_time "同步时间, default 0"
   }
   图书馆笔记适配可行性分析
   可行性分析
   数据结构兼容性：
   现有笔记功能都包含user_email/user_id和book_id字段
   这些字段足以标识图书馆用户和借阅的书籍
   功能完整性：
   现有笔记功能支持创建、查询、更新和删除操作
   支持多端同步
   用户体验一致性：
   用户在普通书城和图书馆中可以使用相同的笔记功能
   符合"一本书的笔记跟随这个人"的要求
   潜在问题
   借阅权限问题：
   当用户归还图书后，是否还能查看/编辑笔记？
   如果再次借阅同一本书，如何保证笔记的连续性？
   数据隔离问题：
   图书馆借阅的书和个人购买的书可能是同一本
   如何区分这两种情况下的笔记？
   多图书馆冲突：
   用户可能在多个图书馆借阅同一本书
   如何处理不同图书馆的同一本书的笔记？
   权限控制：
   图书馆管理员是否需要查看用户笔记的权限？
   如何保护用户笔记隐私？
   数据生命周期：
   用户退出图书馆后，笔记是否保留？
   图书馆关闭后，用户笔记如何处理？
   适配方案
   方案一：直接复用现有笔记功能
   这是最简单的方案，不需要修改数据结构，直接使用现有的笔记功能。

优点：

实现简单，无需额外开发
用户体验一致
缺点：

无法区分普通书城和图书馆的笔记
可能导致数据混淆
方案二：添加来源标识字段
在现有笔记表中添加来源标识字段，用于区分笔记来自普通书城还是图书馆。

Docs/图书馆后台设计方案work.md
file_copy Copy
-- 修改笔记相关表，添加来源标识字段
ALTER TABLE `enyan_user_highlights`
ADD COLUMN `source_type` tinyint DEFAULT 0 COMMENT '来源类型：0-普通书城，1-图书馆',
ADD COLUMN `library_id` bigint DEFAULT 0 COMMENT '图书馆ID，仅当source_type=1时有效';

ALTER TABLE `enyan_user_bookmarks`
ADD COLUMN `source_type` tinyint DEFAULT 0 COMMENT '来源类型：0-普通书城，1-图书馆',
ADD COLUMN `library_id` bigint DEFAULT 0 COMMENT '图书馆ID，仅当source_type=1时有效';

ALTER TABLE `enyan_plan_note`
ADD COLUMN `source_type` tinyint DEFAULT 0 COMMENT '来源类型：0-普通书城，1-图书馆',
ADD COLUMN `library_id` bigint DEFAULT 0 COMMENT '图书馆ID，仅当source_type=1时有效';
优点：

可以区分笔记来源
实现相对简单
缺点：

需要修改现有表结构
需要修改现有查询逻辑
方案三：创建图书馆专用笔记表
为图书馆创建专用的笔记表，结构与现有笔记表类似，但增加图书馆相关字段。

Docs/图书馆后台设计方案work.md
file_copy Copy
-- 图书馆划线笔记表
CREATE TABLE `lib_user_highlights` (
`highlight_id` varchar(255) PRIMARY KEY,
`user_id` bigint default 0 COMMENT '读者ID',
`user_email` varchar(96) default '' COMMENT '用户email',
`library_id` bigint default 0 COMMENT '图书馆ID',
`book_id` bigint default 0 COMMENT '书籍ID',
`content` varchar(250) default '' COMMENT '划线内容',
`create_time` bigint default 0 COMMENT '创建时间',
`rangy` varchar(100) default '' COMMENT 'rangy',
`note_for_highlight` varchar(2000) default '' COMMENT '备注',
`chapter_name` varchar(100) default '' COMMENT '章节标题',
`page_chapter` int default 0 COMMENT '当前章节',
`current_page` int default 0 COMMENT '当前所在章节内的页面',
`total_page` int default 0 COMMENT '本章节总页面数',
`page_process` int default 0 COMMENT '当前进度*1000',
`is_deleted` int default 0 COMMENT '是否已经删除',
`update_time` bigint default 0 COMMENT '同步时间',
`lib_borrow_id` bigint default 0 COMMENT '关联的借阅记录ID'
)collate = utf8mb4_unicode_ci COMMENT='图书馆用户划线笔记表';

-- 图书馆书签表
CREATE TABLE `lib_user_bookmarks` (
-- 类似结构
)collate = utf8mb4_unicode_ci COMMENT='图书馆用户书签表';
优点：

完全隔离图书馆和普通书城的笔记数据
可以添加图书馆特有的字段
缺点：

需要维护两套笔记系统
用户体验可能不一致
数据迁移复杂
推荐方案
综合考虑实现复杂度和用户体验，我推荐方案二：添加来源标识字段，并进行以下适配：

1. 数据模型适配
   enyan-web/src/main/java/com/aaron/spring/service/EnyanUserHighlightsService.java
   file_copy Copy
   /**
* 根据来源类型获取用户划线
* @param email 用户邮箱
* @param bookId 书籍ID
* @param sourceType 来源类型：0-普通书城，1-图书馆
* @param libraryId 图书馆ID，仅当sourceType=1时有效
* @return 划线列表
  */
  List<EnyanUserHighlights> findHighlightsBySourceType(String email, Long bookId, Integer sourceType, Long libraryId);

/**
* 保存图书馆划线
* @param highlight 划线对象
* @param libraryId 图书馆ID
* @return 保存结果
  */
  int saveLibraryHighlight(EnyanUserHighlights highlight, Long libraryId);
2. 服务层适配
   enyan-web/src/main/java/com/aaron/spring/service/impl/EnyanUserHighlightsServiceImpl.java
   file_copy Copy
   @Override
   public List<EnyanUserHighlights> findHighlightsBySourceType(String email, Long bookId, Integer sourceType, Long libraryId) {
   EnyanUserHighlightsExample example = new EnyanUserHighlightsExample();
   EnyanUserHighlightsExample.Criteria criteria = example.createCriteria();
   criteria.andUserEmailEqualTo(email)
   .andBookIdEqualTo(bookId)
   .andIsDeletedEqualTo(0)
   .andSourceTypeEqualTo(sourceType);

   if (sourceType == 1 && libraryId != null) {
   criteria.andLibraryIdEqualTo(libraryId);
   }

   return enyanUserHighlightsMapper.selectByExample(example);
   }

@Override
public int saveLibraryHighlight(EnyanUserHighlights highlight, Long libraryId) {
highlight.setSourceType(1);
highlight.setLibraryId(libraryId);
return this.save(highlight);
}
3. 控制器适配
   enyan-web/src/main/java/com/aaron/spring/api/v4/controller/RestLibraryController.java
   file_copy Copy
   /**
* 获取图书馆书籍划线
  */
  @RequestMapping(value = "/highlights", method = RequestMethod.POST)
  public ExecuteResult<List<RestHighlight>> getLibraryHighlights(@RequestBody RestHighlight request,
  HttpServletRequest httpRequest) {
  request.initHeaderValue(httpRequest);
  ExecuteResult<List<RestHighlight>> result = new ExecuteResult<>();

  if (StringUtils.isBlank(request.getEmail()) || request.getBookId() == null || request.getLibraryId() == null) {
  result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
  return result;
  }

  List<EnyanUserHighlights> highlights = enyanUserHighlightsService.findHighlightsBySourceType(
  request.getEmail(), request.getBookId(), 1, request.getLibraryId());

  List<RestHighlight> restHighlights = highlights.stream()
  .map(h -> {
  RestHighlight rh = new RestHighlight();
  rh.initFrom(h);
  return rh;
  })
  .collect(Collectors.toList());

  result.setResult(restHighlights);
  return result;
  }

/**
* 保存图书馆书籍划线
  */
  @RequestMapping(value = "/saveHighlight", method = RequestMethod.POST)
  public ExecuteResult<RestHighlight> saveLibraryHighlight(@RequestBody RestHighlight request,
  HttpServletRequest httpRequest) {
  request.initHeaderValue(httpRequest);
  ExecuteResult<RestHighlight> result = new ExecuteResult<>();

  if (StringUtils.isBlank(request.getEmail()) || request.getBookId() == null ||
  request.getLibraryId() == null || StringUtils.isBlank(request.getContent())) {
  result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
  return result;
  }

  // 检查用户是否有权限（是否正在借阅该书）
  boolean hasBorrowed = libraryBorrowService.checkUserBorrowing(
  request.getUserId(), request.getLibraryId(), request.getBookId());

  if (!hasBorrowed) {
  result.addErrorMessage("您没有借阅该书或借阅已过期");
  return result;
  }

  EnyanUserHighlights highlight = new EnyanUserHighlights();
  highlight.setHighlightId(UUID.randomUUID().toString());
  highlight.setUserEmail(request.getEmail());
  highlight.setUserId(request.getUserId());
  highlight.setBookId(request.getBookId());
  highlight.setContent(request.getContent());
  highlight.setRangy(request.getRangy());
  highlight.setNoteForHighlight(request.getNoteForHighlight());
  highlight.setChapterName(request.getChapterName());
  highlight.setPageChapter(request.getPageChapter());
  highlight.setCurrentPage(request.getCurrentPage());
  highlight.setTotalPage(request.getTotalPage());
  highlight.setPageProcess(request.getPageProcess());
  highlight.setCreateTime(System.currentTimeMillis());
  highlight.setUpdateTime(System.currentTimeMillis());
  highlight.setIsDeleted(0);

  enyanUserHighlightsService.saveLibraryHighlight(highlight, request.getLibraryId());

  RestHighlight restHighlight = new RestHighlight();
  restHighlight.initFrom(highlight);
  result.setResult(restHighlight);

  return result;
  }
4. 借阅权限检查
   enyan-web/src/main/java/com/aaron/spring/service/LibraryBorrowService.java
   file_copy Copy
   /**
* 检查用户是否正在借阅某本书
* @param userId 用户ID
* @param libraryId 图书馆ID
* @param bookId 书籍ID
* @return 是否正在借阅
  */
  boolean checkUserBorrowing(Long userId, Long libraryId, Long bookId);
  enyan-web/src/main/java/com/aaron/spring/service/impl/LibraryBorrowServiceImpl.java
  file_copy Copy
  @Override
  public boolean checkUserBorrowing(Long userId, Long libraryId, Long bookId) {
  LibraryBorrowExample example = new LibraryBorrowExample();
  example.createCriteria()
  .andUserIdEqualTo(userId)
  .andLibraryIdEqualTo(libraryId)
  .andBookIdEqualTo(bookId)
  .andStatusEqualTo(0); // 0-借阅中

  return libraryBorrowMapper.countByExample(example) > 0;
  }
  解决潜在问题的策略
  借阅权限问题：
  创建笔记时检查借阅状态
  查询笔记时不检查借阅状态，允许用户查看历史笔记
  归还后禁止编辑，但允许查看
  数据隔离问题：
  通过source_type和library_id字段区分笔记来源
  在UI层面可以合并展示，但后台数据保持隔离
  多图书馆冲突：
  通过library_id字段区分不同图书馆的笔记
  用户可以选择查看特定图书馆的笔记或合并查看
  权限控制：
  默认图书馆管理员无权查看用户笔记
  可以添加隐私政策说明用户笔记的使用范围
  数据生命周期：
  用户退出图书馆后，笔记仍然保留
  图书馆关闭后，相关笔记标记为历史数据，但不删除
  总结
  现有笔记功能可以通过添加来源标识字段的方式适配到图书馆场景，满足"一本书的笔记跟随这个人"的要求。这种方案实现相对简单，同时保持了用户体验的一致性。

需要注意的是，在实现过程中要处理好借阅权限、数据隔离、多图书馆冲突、权限控制和数据生命周期等问题，确保用户笔记数据的安全和完整性。







-----图书管评论功能设计方案-----
搜索下系统中的评分（评论）功能，是否可以直接用在图书馆中，希望是评论在enyanbook范围内全局都是共享的，如果需要改造，要什么方案

Augment
图书馆评分（评论）功能适配分析
现有评论功能分析
根据代码库，我找到了系统中的评论功能相关代码：

评论模型 (EnyanComment)
enyan-web/src/main/java/com/aaron/spring/model/EnyanComment.java
file_copy Copy
public class EnyanComment extends BaseDTO{
private static final long serialVersionUID = 1L;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private String title;

    private String content;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    private String email;

    private String nickName;

    private Integer sex;

    private Integer likeCount;

    private Integer commentCount;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long parentId;

    private Integer canShow;

    private Integer isDeleted;

    private Date createAt;
    
    private String star;
    
    private Long count;
    // ...
}
评论服务接口 (EnyanCommentService)
enyan-web/src/main/java/com/aaron/spring/service/EnyanCommentService.java
file_copy Copy
public interface EnyanCommentService extends IService<EnyanComment, EnyanCommentExample>{

    /**
     * <p>增加喜欢量</p>
     * @param id
     * @return int
     * @since : 2023/4/21
     **/
    int updateCommentLikeCountById(Long id);

    /**
     * <p>删除评论，如果是子评论还需要减父评论的计数</p>
     * @param id
     * @param email
     * @return int
     * @since : 2023/4/23
     **/
    int updateCommentToDeletedById(Long id, String email, Long parentId);
}
评论自定义Mapper (EnyanCommentCustomMapper)
enyan-web/src/main/java/com/aaron/spring/mapper/custom/EnyanCommentCustomMapper.java
file_copy Copy
@Repository
public interface EnyanCommentCustomMapper {
@Update({"update enyan_comment set like_count = like_count + 1 where data_id = #{dataId}"})
int updateCommentLikeCountById(@Param("dataId") Long dataId);

    /**
     * <p>删除评论，另外，如果没有子评论则直接设置为不可统计（不管是父评论，还是子评论--子评论默认comment_count是0）</p>
     * @param dataId
     * @param email
     * @return int
     * @since : 2023/5/29
     **/
    @Update({"update enyan_comment set is_deleted = 1,can_show = IF(comment_count = 0, 0, can_show) where data_id = #{dataId} and email = #{email}"})
    int updateCommentToDeletedById(@Param("dataId") Long dataId, @Param("email") String email);
    
    @Select("select star, count(*) as count from enyan_comment where book_id = #{bookId} and parent_id = 0 and can_show = 1 and is_deleted = 0 and star != '0' group by star")
    List<EnyanComment> findStarDataByBook(@Param("bookId") Long bookId);
}
评论控制器 (ShopController)
enyan-web/src/main/java/com/aaron/spring/controller/ShopController.java
file_copy Copy
@RequestMapping(value = "/addComment", method = RequestMethod.POST)
public ExecuteResult<RestComment> addComment(@RequestBody RestComment restObj, HttpServletRequest request) {
ExecuteResult<RestComment> result = new ExecuteResult<>();
if (StringUtils.isBlank(restObj.getEmail()) || StringUtils.isBlank(restObj.getContent())
|| null == restObj.getBookId()){
result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
return result;
}

    EnyanComment obj = new EnyanComment();
    obj.setContent(restObj.getContent());
    obj.setBookId(restObj.getBookId());
    obj.setEmail(restObj.getEmail());
    obj.setLikeCount(0);
    obj.setNickName(restObj.getNickName());
    obj.setSex(restObj.getSex());//0:默认；1：男；2：女
    if (null == restObj.getParentId()){
        restObj.setParentId(0L);//默认parentId=0
    }
    obj.setCanShow(1);
    obj.setParentId(restObj.getParentId());
    obj.setTitle(restObj.getTitle());
    obj.setIsDeleted(0);
    obj.setCreateAt(new Date());
    obj.setCommentCount(0);
    obj.setStar(restObj.getStar());
    enyanCommentService.addRecord(obj);
    if (restObj.getParentId() == 0 && "0".equals(restObj.getStar()) == false){
        enyanBookService.resetBookStar(restObj.getBookId());
    }
    return result;
}
书籍评分更新 (EnyanBookService)
enyan-web/src/main/java/com/aaron/spring/service/impl/EnyanBookServiceImpl.java
file_copy Copy
@Override
public void resetBookStar(Long bookId) {
List<EnyanComment> resultList = enyanCommentCustomMapper.findStarDataByBook(bookId);
if (resultList.isEmpty() == true){
return;
}
EnyanComment comment = resultList.get(0);
String star = comment.getStar();
Long count = comment.getCount();
if (StringUtils.isBlank(star) || null == count){
star = "";
count = 0L;
}
enyanBookCustomMapper.updateBookStarByBookId(bookId,star,count.intValue());
dataInterface.delBook(bookId);
}
enyan-web/src/main/java/com/aaron/spring/mapper/custom/EnyanBookCustomMapper.java
file_copy Copy
@Update({"update enyan_book set star = #{star}, star_Count=#{starCount} where book_id = #{bookId}"})
int updateBookStarByBookId(@Param("bookId")Long bookId, @Param("star")String star, @Param("starCount")Integer starCount);
图书馆评论功能适配可行性分析
需求分析
根据需求，希望评论在enyanbook范围内全局共享，即无论是普通书城还是图书馆，用户对同一本书的评论应该是共享的。

可行性分析
数据结构兼容性：
现有评论功能主要依赖book_id关联书籍
没有特定字段区分评论来源（书城或图书馆）
功能完整性：
现有评论功能支持添加、查询、点赞和删除操作
支持评分功能，并更新书籍的平均评分
用户体验一致性：
用户在普通书城和图书馆中可以看到相同的评论内容
符合"评论在enyanbook范围内全局都是共享的"要求
潜在问题
权限控制问题：
图书馆用户是否有权限评论他们没有购买但借阅的书籍？
如何区分图书馆用户和普通用户的评论？
评论管理问题：
图书馆管理员是否需要管理评论的权限？
如何处理不适当的评论内容？
用户身份问题：
如何标识评论来自图书馆用户还是普通用户？
是否需要在评论中显示用户的身份？
评分影响问题：
图书馆用户的评分是否应该与普通用户的评分同等对待？
评分可能会影响书籍在书城的排名和销售
适配方案
方案一：直接复用现有评论功能
这是最简单的方案，不需要修改数据结构，直接使用现有的评论功能。

优点：

实现简单，无需额外开发
评论完全共享，符合需求
缺点：

无法区分评论来源（书城或图书馆）
可能存在权限控制问题
方案二：添加来源标识字段
在现有评论表中添加来源标识字段，用于区分评论来自普通书城还是图书馆。

Docs/图书馆后台设计方案work.md
file_copy Copy
-- 修改评论表，添加来源标识字段
ALTER TABLE `enyan_comment`
ADD COLUMN `source_type` tinyint DEFAULT 0 COMMENT '来源类型：0-普通书城，1-图书馆',
ADD COLUMN `library_id` bigint DEFAULT 0 COMMENT '图书馆ID，仅当source_type=1时有效';
优点：

可以区分评论来源
保持评论共享，同时提供更多分析维度
缺点：

需要修改现有表结构
需要修改现有查询逻辑
